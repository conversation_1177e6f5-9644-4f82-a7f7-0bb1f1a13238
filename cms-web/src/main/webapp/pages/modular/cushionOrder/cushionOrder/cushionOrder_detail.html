@layout("/common/_container.html",{js:["/pages/modular/settleProjects/settleProjects/js/fileupd.js","/pages/modular/cushionOrder/cushionOrder/js/cushionOrder_detail.js"]}){

<style>
    .custom-card {
        margin: 15px 0;
        border-radius: 4px;
    }


    .data-label {
        color: #666;
        font-size: 14px;
    }

    .data-value {
        font-size: 20px;
        color: #333;
        margin: 8px 0;
    }

    .status-badge {
        background: #5FB878;
        color: white;
        padding: 2px 8px;
        border-radius: 3px;
    }

    .attachment-link {
        color: #5FB878;
        margin-right: 15px;
    }
</style>
<div class="layui-container" style="margin-top: 20px; background: white;width: 100%">
    <!-- 标题行 -->
    <div class="layui-row">
        <div class="layui-col-md12">
            <div class="custom-card layui-card">
                <div class="layui-card-header" style="border-left: 4px solid #5FB878;">
                    <h2>${item.settleProjects.projectName} <span style="color: #666;font-size: 16px;">${item.settleProjects.projectCode} </span>
                        <span class="status-badge">${item.settleProjects.projectStatusName}</span>
                    </h2>
                </div>
            </div>
        </div>
    </div>

    <!-- 营收数据 -->
    <div class="layui-row">
        <div class="layui-col-md12">
            <div class="custom-card layui-card data-panel">
                <div class="layui-card-header card-title">项目营收数据</div>

                <div class="layui-card-body">
                    <!-- 第一行 -->
                    <div class="layui-row data-row">
                        <div class="layui-col-md3 data-item">
                            <label class="data-label">总营收金额：</label>
                            <span class="data-value highlight">${item.cushionOrder.liushuiAmount}</span>
                        </div>
                        <div class="layui-col-md3 data-item">
                            <label class="data-label">总应发放收益：</label>
                            <span class="data-value warning">${item.cushionOrder.yingfaAmount}</span>
                        </div>
                        <div class="layui-col-md3 data-item">
                            <label class="data-label">总已发放收益：</label>
                            <span class="data-value success">${item.cushionOrder.yifaAmount}</span>
                        </div>
                        <div class="layui-col-md3 data-item">
                            <label class="data-label">参与结算人数：</label>
                            <span class="data-value">${item.cushionOrder.cushionPnum}</span>
                        </div>
                    </div>

                    <!-- 第二行 -->
                    <div class="layui-row data-row">
                        <div class="layui-col-md3 data-item">
                            <label class="data-label">达人已提现金额：</label>
                            <span class="data-value success">${item.cushionOrder.withdrawnAmount}</span>
                        </div>
                        <div class="layui-col-md3 data-item">
                            <label class="data-label">达人未提现金额：</label>
                            <span class="data-value warning">${item.cushionOrder.unwithdrawnAmount}</span>
                        </div>
                        <div class="layui-col-md3 data-item">
                            <label class="data-label">达人已到账金额：</label>
                            <span class="data-value success">${item.cushionOrder.arriveAmount}</span>
                        </div>
                        <div class="layui-col-md3 data-item">
                            <label class="data-label">达人未到账金额：</label>
                            <span class="data-value warning">${item.cushionOrder.noarriveAmount}</span>
                        </div>
                    </div>

                    <!-- 第三行 -->
                    <div class="layui-row data-row">
                        <div class="layui-col-md4 data-item">
                            <label class="data-label">非标结算人数及比例：</label>
                            <span class="data-value">
                            ${ (isEmpty(item.cushionOrder.feibiaoPnum) && isEmpty(item.cushionOrder.feibiaoRate))
                                ? "-"
                                : item.cushionOrder.feibiaoPnum! + " ("+item.cushionOrder.feibiaoRate!+"%)" }
                        </span>
                        </div>
                        <div class="layui-col-md4 data-item">
                            <label class="data-label">非标结算金额及比例：</label>
                            <span class="data-value">
                            ${ (isEmpty(item.cushionOrder.feibiaoAmount) && isEmpty(item.cushionOrder.feibiaoAmountRate))
                                ? "-"
                                : item.cushionOrder.feibiaoAmount! + " ("+item.cushionOrder.feibiaoAmountRate!+"%)" }
                        </span>
                        </div>
                        <div class="layui-col-md4 data-item">
                            <label class="data-label">结算异常比例：</label>
                            <span class="data-value danger">
                            ${ (isEmpty(item.cushionOrder.cushionYichangNum) && isEmpty(item.cushionOrder.cushionYichangRate))
                                ? "-"
                                : item.cushionOrder.cushionYichangRate! + "%" }
                        </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        /* 增强样式 */
        .data-panel {
            margin: 15px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, .1);
        }

        .card-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            border-bottom: 1px solid #eee;
        }

        .data-row {
            border-bottom: 1px dashed #eee;
        }

        .data-item {
            padding: 8px 15px;
            display: flex;
            align-items: center;
        }

        .data-label {
            width: 140px;
            color: #666;
            margin-right: 10px;
            flex-shrink: 0;
        }

        .data-value {
            color: #333;
            font-weight: 500;
        }

        /* 颜色标记 */
        .highlight {
            color: #1890ff;
        }

        /* 蓝色 */
        .success {
            color: #52c41a;
        }

        /* 绿色 */
        .warning {
            color: #faad14;
        }

        /* 橙色 */
        .danger {
            color: #ff4d4f;
        }

        /* 红色 */

        /* 老板和财务主管角色专用样式 - 服务端渲染 */
        @if(shiro.hasAnyRoles("老板,财务主管")){
        .data-value.highlight,
        .data-value.warning,
        .data-value.success,
        .data-value.danger,
        .data-value {
            color: #666 !important;
            font-size: 14px !important;
            font-weight: 400 !important;
            line-height: 20px !important;
        }

        .layui-table td {
            color: #666 !important;
            font-size: 14px !important;
            font-weight: 400 !important;
            line-height: 20px !important;
        }

        .status-badge {
            color: #666 !important;
            background: #f0f0f0 !important;
            font-size: 14px !important;
            font-weight: 400 !important;
            line-height: 20px !important;
        }
        @}
    </style>


    <div class="layui-row">
        <div class="layui-col-md12">
            <div class="custom-card layui-card data-panel">
                <div class="layui-card-header card-title">项目基础信息</div>

                <div class="layui-card-body">
                    <!-- 第一行 -->
                    <div class="layui-row data-row">
                        <div class="layui-col-md3 data-item">
                            <label class="data-label">项目简称：</label>
                            <span class="data-value highlight">${item.settleProjects.projectName}</span>
                        </div>
                        <div class="layui-col-md3 data-item">
                            <label class="data-label">项目编号：</label>
                            <span class="data-value warning">${item.settleProjects.projectCode}</span>
                        </div>
                        <div class="layui-col-md3 data-item">
                            <label class="data-label">项目状态：</label>
                            <span class="data-value success">${item.settleProjects.projectStatusName}</span>
                        </div>
                        <div class="layui-col-md3 data-item">
                            <label class="data-label">业务负责人：</label>
                            <span class="data-value">${item.settleProjects.businessOwnerName}</span>
                        </div>
                    </div>


                    <div class="layui-row data-row">
                        <div class="layui-col-md3 data-item">
                            <label class="data-label">项目类型：</label>
                            <span class="data-value highlight">${item.settleProjects.projectTypeName}</span>
                        </div>
                        <div class="layui-col-md3 data-item">
                            <label class="data-label">项目周期：</label>
                            <span class="data-value warning">${item.settleProjects.projectStarttime, dateFormat='yyyy-MM-dd'} - ${item.settleProjects.projectEndtime, dateFormat='yyyy-MM-dd'}</span>
                        </div>
                        <div class="layui-col-md3 data-item">
                            <label class="data-label">创建时间：</label>
                            <span class="data-value success">${item.settleProjects.createTime, dateFormat='yyyy-MM-dd'}</span>
                        </div>
                        <div class="layui-col-md3 data-item">
                            <label class="data-label">创建人：</label>
                            <span class="data-value">${item.settleProjects.createName}</span>
                        </div>
                    </div>


                    <div class="layui-row data-row">
                        <div class="layui-col-md3 data-item">
                            <label class="data-label">服务平台：</label>
                            <span class="data-value highlight">${item.settleProjects.platform}</span>
                        </div>
                        <div class="layui-col-md3 data-item">
                            <label class="data-label">参与公司主体：</label>
                            <span class="data-value warning">${item.settleProjects.compName}</span>
                        </div>
                        <div class="layui-col-md3 data-item">
                            <label class="data-label">机构名称：</label>
                            <span class="data-value success">${item.settleProjects.jigouName}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="layui-row">
        <div class="layui-col-md12">
            <div class="custom-card layui-card data-panel">
                <div class="layui-card-header card-title">验收和结算</div>

                <div class="layui-card-body">
                    <!-- 第一行 -->
                    <div class="layui-row data-row">
                        <div class="layui-col-md3 data-item">
                            <label class="data-label">结算分成和比例（公司/达人）：</label>
                            <span class="data-value highlight">
                                @var cstr = "";
                                @if(item.settleProjects.commissionToComp!=null){
                                    @cstr=Objects.append(item.settleProjects.commissionToComp,"|",item.settleProjects.commissionToDaren);
                                @};
                                ${cstr}
                        </span>
                        </div>
                        <div class="layui-col-md3 data-item">
                            <label class="data-label">达人结算周期：</label>
                            <span class="data-value warning">${item.settleProjects.settlementCycleName}</span>
                        </div>
                        <div class="layui-col-md3 data-item">
                            <label class="data-label">收益类型：</label>
                            <span class="data-value success">${item.settleProjects.revenueTypeName}</span>
                        </div>
                        <div class="layui-col-md3 data-item">
                            <label class="data-label">机构收益：</label>
                            <span class="data-value">${item.settleProjects.agencyRevenue == 1 ? "有收益" : "无收益"}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <!-- 背景介绍 -->
    <div class="layui-row">
        <div class="layui-col-md12">
            <div class="custom-card layui-card">
                <div class="layui-card-header">量化目标</div>
                <div class="layui-card-body">
                    <p>${item.settleProjects.quantTarget}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 背景介绍 -->
    <div class="layui-row">
        <div class="layui-col-md12">
            <div class="custom-card layui-card">
                <div class="layui-card-header">项目背景介绍</div>
                <div class="layui-card-body">
                    <p>${item.settleProjects.projectDesc}</p>
                    <div class="layui-input-block" style="margin-left:0px">
                        <div class="file-list" id="beijingFileDiv">
                            <!-- 动态插入的文件项 -->
                        </div>
                        <input type="hidden" id="beijingFile" name="beijingFile"/>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 执行方案 -->
    <div class="layui-row">
        <div class="layui-col-md12">
            <div class="custom-card layui-card">
                <div class="layui-card-header">项目执行方案及目标</div>
                <div class="layui-card-body">
                    <p>${item.settleProjects.projectZhixingFa}</p>
                    <div class="layui-input-block"  style="margin-left:0px">
                        <div id="zhixingFileDiv" class="layui-clear">
                            <!-- 动态插入文件项 -->
                        </div>
                        <input type="hidden" id="zhixingFiles" name="zhixingFiles"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- HTML部分 -->
@if(!shiro.hasAnyRoles("财务,老板")){
<div class="btn-container">
    <button id="btnAdd" class="layui-btn custom-btn">
        <i class="layui-icon">&#xe654;</i>
        <span class="btn-text">添加</span>
    </button>
</div>
@}

<style>
    /* 容器样式确保按钮独占一行 */
    .btn-container {
        height:55px;
        width: 100%;
        background: white;
    }


    /* 图标和文字间距 */
    .custom-btn .layui-icon {
        margin-right: 8px;
        font-size: 18px;
        vertical-align: middle;
    }

    /* 文字样式 */
    .btn-text {
        vertical-align: middle;
    }

</style>
<table class="layui-table" id="cushionBatchTable" lay-filter="cushionBatchTable"></table>
<div style="color:red; padding:10px; border:1px solid #e6e6e6; margin:10px 0">
    示例:机构收益为"是"
</div>
<table class="layui-table" id="cushionBatchTableJigou" lay-filter="cushionBatchTableJigou"></table>


<script type="text/html" id="tableBar">

    {{# if(d.cushionBatch.batchStatus === 3004 || d.cushionBatch.batchStatus === 3005 ) { }}
    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="edit">修改</a>
    {{# } else if(d.cushionBatch.batchStatus === 2003 && d.wfApprovalRecordList ) { }}
        @if(shiro.hasAnyRoles("老板")){
            {{# if(d.showLb==1) { }}
            <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="lbAudit">老板审批</a>
            {{# } }}
        @}
        @if(shiro.hasAnyRoles("财务主管")){
            {{# if(d.showCw==1) { }}
            <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="cwAudit">财务审批</a>
            {{# } }}
        @}
    {{# } }}

    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="darenDetail">达人详情</a>
    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="dowloadJiesuan">下载结算单</a>
</script>
<div class="layui-row">
    <div class="layui-col-24" style="text-align: center; padding: 20px 0;">
        <button class="layui-btn layui-btn-primary layui-btn-lg" id="backupPage"   style="width: 200px; margin-left: 30px;">返回</button>
    </div>
</div>
</div>
@}
