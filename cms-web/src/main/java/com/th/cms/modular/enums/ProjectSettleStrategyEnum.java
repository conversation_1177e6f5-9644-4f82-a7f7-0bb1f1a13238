package com.th.cms.modular.enums;

public enum ProjectSettleStrategyEnum {

    PDD("PDD",null,"拼多多"),

    TAOBAO_ZB("TBSC",75,"淘宝-直播"),
    TAOBAO_XD("TBXD",74,"淘宝-小店"),
    TAOBAO_QQ("TBQQ",76,"淘宝-全勤"),
    TAOBAO_DS("TBDS",78,"淘宝-打赏"),
    TAOBAO_NRDH("TAOBAO_NRDH",35,"淘宝-内容带货"), //暂时没有淘宝内容带货

    TAOBAO_GG("TBGG",72,"逛逛"),

    SHIPINHAO_CZFC("CZFC",40,"创作分成"),

    SHIPINHAO_HXPT("HXPT",41,"互选平台"),

    BAIDU_BJH("BAIDU_BJH",65,"百家号"),

    MEITUAN("MEITUAN",63,"美团优选"),

    ZHIF<PERSON><PERSON><PERSON>("ZHIFUBAO",55,"支付宝"),

    TAOBAO_ZB_DF("TAOBAO_ZB_DF",77,"淘宝-直播-垫付"),
    ;

    public  String code;


    /**
     * 平台ID
     */
    private Integer platformId;

    private String platformProject;

    ProjectSettleStrategyEnum(String code,Integer platformId,String platformProject) {
        this.code = code;
        this.platformId = platformId;
        this.platformProject = platformProject;
    }


    public Integer getPlatformId() {
        return platformId;
    }

    public String getPlatformProject() {
        return platformProject;
    }

    public String getCode() {
        return code;
    }

    public static ProjectSettleStrategyEnum fromCode(String code) {
        for (ProjectSettleStrategyEnum settleStrategy : ProjectSettleStrategyEnum.values()) {
            if (settleStrategy.getCode().equals(code)) {
                return settleStrategy;
            }
        }
        return null;
    }

    public static ProjectSettleStrategyEnum fromPlatformProject(String platformProject) {
        for (ProjectSettleStrategyEnum settleStrategy : ProjectSettleStrategyEnum.values()) {
            if (settleStrategy.getPlatformProject().equals(platformProject)) {
                return settleStrategy;
            }
        }
        return null;
    }

    public static ProjectSettleStrategyEnum fromPlatformId(Integer platformId) {
        for (ProjectSettleStrategyEnum settleStrategy : ProjectSettleStrategyEnum.values()) {
            if (settleStrategy.getPlatformId() == platformId) {
                return settleStrategy;
            }
        }
        return null;
    }

}
